import {
  IsNotEmpty,
  IsString,
  IsEnum,
  IsOptional,
  IsInt,
  IsUrl,
  Length,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { LandingPageType } from 'src/database/entities/category/category-banner.entity';

export class CreateCategoryBannerDto {
  @IsInt()
  @IsNotEmpty()
  @Type(() => Number)
  category_id: number;

  @IsInt()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  sort_order?: number = 0;

  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  title: string;

  @IsEnum(LandingPageType)
  @IsNotEmpty()
  landing_page_type: LandingPageType;

  @IsString()
  @IsNotEmpty()
  @Length(1, 500)
  landing_page_url: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  web_url?: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  tab_url?: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  mobile_url?: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  icon_url?: string;
}

export class UpdateCategoryBannerDto {
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  category_id?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  sort_order?: number;

  @IsOptional()
  @IsString()
  @Length(1, 255)
  title?: string;

  @IsOptional()
  @IsEnum(LandingPageType)
  landing_page_type?: LandingPageType;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  landing_page_url?: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  web_url?: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  tab_url?: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  mobile_url?: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  icon_url?: string;
}

export class CategoryBannerQueryDto {
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  category_id?: number;

  @IsOptional()
  @IsEnum(LandingPageType)
  landing_page_type?: LandingPageType;
}
