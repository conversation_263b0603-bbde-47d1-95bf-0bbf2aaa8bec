# Category Banner API Documentation

## Overview

The Category Banner feature allows you to associate multiple banners with categories in a one-to-many relationship. Each banner contains information about landing pages, URLs for different platforms, and display order.

## Database Schema

### Table: `category_banner`

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | int(11) | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| `category_id` | int(11) | NOT NULL, FOREIGN KEY | References `catalog_category.id` |
| `sort_order` | int(11) | DEFAULT 0 | Display order (ascending) |
| `title` | varchar(255) | NOT NULL | Banner title |
| `landing_page_type` | enum('product','category') | NOT NULL | Type of landing page |
| `landing_page_url` | varchar(500) | NOT NULL | Main landing page URL |
| `web_url` | varchar(500) | NULLABLE | Web-specific URL |
| `tab_url` | varchar(500) | NULLABLE | Tab-specific URL |
| `mobile_url` | varchar(500) | NULLABLE | Mobile-specific URL |
| `icon_url` | varchar(500) | NULLABLE | Icon/image URL |
| `created_at` | datetime | NOT NULL | Creation timestamp |
| `updated_at` | datetime | NOT NULL | Last update timestamp |

### Relationships

- **One-to-Many**: `catalog_category` → `category_banner`
- **Foreign Key**: `category_banner.category_id` → `catalog_category.id` (CASCADE DELETE)

## API Endpoints

### Base URL: `/v1/catalog-admin/category`

### 1. Create Category Banner

**POST** `/banners`

Creates a new banner for a category.

**Request Body:**
```json
{
  "category_id": 1,
  "sort_order": 0,
  "title": "Featured Products",
  "landing_page_type": "product",
  "landing_page_url": "/products/featured",
  "web_url": "https://example.com/featured",
  "tab_url": "https://example.com/tab/featured",
  "mobile_url": "https://example.com/mobile/featured",
  "icon_url": "https://example.com/icons/featured.png"
}
```

**Response:**
```json
{
  "id": 1,
  "category_id": 1,
  "sort_order": 0,
  "title": "Featured Products",
  "landing_page_type": "product",
  "landing_page_url": "/products/featured",
  "web_url": "https://example.com/featured",
  "tab_url": "https://example.com/tab/featured",
  "mobile_url": "https://example.com/mobile/featured",
  "icon_url": "https://example.com/icons/featured.png",
  "created_at": "2024-01-01T00:00:00.000Z",
  "updated_at": "2024-01-01T00:00:00.000Z"
}
```

### 2. Get All Category Banners

**GET** `/banners?category_id=1&landing_page_type=product`

Retrieves all banners with optional filtering.

**Query Parameters:**
- `category_id` (optional): Filter by category ID
- `landing_page_type` (optional): Filter by landing page type ('product' or 'category')

**Response:**
```json
[
  {
    "id": 1,
    "category_id": 1,
    "sort_order": 0,
    "title": "Featured Products",
    "landing_page_type": "product",
    "landing_page_url": "/products/featured",
    "web_url": "https://example.com/featured",
    "tab_url": "https://example.com/tab/featured",
    "mobile_url": "https://example.com/mobile/featured",
    "icon_url": "https://example.com/icons/featured.png",
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z",
    "category": {
      "id": 1,
      "name": "Electronics",
      "status": true
    }
  }
]
```

### 3. Get Category Banner by ID

**GET** `/banners/:id`

Retrieves a specific banner by its ID.

**Response:**
```json
{
  "id": 1,
  "category_id": 1,
  "sort_order": 0,
  "title": "Featured Products",
  "landing_page_type": "product",
  "landing_page_url": "/products/featured",
  "web_url": "https://example.com/featured",
  "tab_url": "https://example.com/tab/featured",
  "mobile_url": "https://example.com/mobile/featured",
  "icon_url": "https://example.com/icons/featured.png",
  "created_at": "2024-01-01T00:00:00.000Z",
  "updated_at": "2024-01-01T00:00:00.000Z",
  "category": {
    "id": 1,
    "name": "Electronics",
    "status": true
  }
}
```

### 4. Get Banners by Category ID

**GET** `/:categoryId/banners`

Retrieves all banners for a specific category.

**Response:**
```json
[
  {
    "id": 1,
    "category_id": 1,
    "sort_order": 0,
    "title": "Featured Products",
    "landing_page_type": "product",
    "landing_page_url": "/products/featured",
    "web_url": "https://example.com/featured",
    "tab_url": "https://example.com/tab/featured",
    "mobile_url": "https://example.com/mobile/featured",
    "icon_url": "https://example.com/icons/featured.png",
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z"
  }
]
```

### 5. Update Category Banner

**PATCH** `/banners/:id`

Updates an existing banner.

**Request Body:**
```json
{
  "title": "Updated Banner Title",
  "sort_order": 1,
  "web_url": "https://example.com/updated"
}
```

**Response:**
```json
{
  "id": 1,
  "category_id": 1,
  "sort_order": 1,
  "title": "Updated Banner Title",
  "landing_page_type": "product",
  "landing_page_url": "/products/featured",
  "web_url": "https://example.com/updated",
  "tab_url": "https://example.com/tab/featured",
  "mobile_url": "https://example.com/mobile/featured",
  "icon_url": "https://example.com/icons/featured.png",
  "created_at": "2024-01-01T00:00:00.000Z",
  "updated_at": "2024-01-01T00:00:01.000Z"
}
```

### 6. Delete Category Banner

**DELETE** `/banners/:id`

Deletes a banner.

**Response:**
```json
{
  "message": "Category banner deleted successfully"
}
```

## Validation Rules

### CreateCategoryBannerDto
- `category_id`: Required, must be a valid integer
- `sort_order`: Optional, must be >= 0, defaults to 0
- `title`: Required, 1-255 characters
- `landing_page_type`: Required, must be 'product' or 'category'
- `landing_page_url`: Required, 1-500 characters
- `web_url`: Optional, 1-500 characters
- `tab_url`: Optional, 1-500 characters
- `mobile_url`: Optional, 1-500 characters
- `icon_url`: Optional, 1-500 characters

### UpdateCategoryBannerDto
- All fields are optional
- Same validation rules as create when provided

## Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": ["title should not be empty", "landing_page_type must be a valid enum value"],
  "error": "Bad Request"
}
```

### 404 Not Found
```json
{
  "statusCode": 404,
  "message": "Category banner with ID 1 not found",
  "error": "Not Found"
}
```

### 500 Internal Server Error
```json
{
  "statusCode": 500,
  "message": "Failed to create category banner",
  "error": "Internal Server Error"
}
```

## Installation

1. Run the SQL migration:
```sql
-- Execute the SQL file: database-migrations/create-category-banner-table.sql
```

2. The TypeORM entities and API endpoints are already configured and ready to use.

## Usage Examples

### Create a product banner
```bash
curl -X POST http://localhost:3000/v1/catalog-admin/category/banners \
  -H "Content-Type: application/json" \
  -d '{
    "category_id": 1,
    "title": "Best Sellers",
    "landing_page_type": "product",
    "landing_page_url": "/products/best-sellers",
    "sort_order": 1
  }'
```

### Get all banners for a category
```bash
curl http://localhost:3000/v1/catalog-admin/category/1/banners
```

### Update a banner
```bash
curl -X PATCH http://localhost:3000/v1/catalog-admin/category/banners/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated Title",
    "sort_order": 2
  }'
```
