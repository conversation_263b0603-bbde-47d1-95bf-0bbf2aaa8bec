-- Create category_banner table
-- This table stores banner information associated with categories
-- One category can have multiple banners (one-to-many relationship)

CREATE TABLE IF NOT EXISTS `category_banner` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `title` varchar(255) NOT NULL,
  `landing_page_type` enum('product','category') NOT NULL,
  `landing_page_url` varchar(500) NOT NULL,
  `web_url` varchar(500) DEFAULT NULL,
  `tab_url` varchar(500) DEFAULT NULL,
  `mobile_url` varchar(500) DEFAULT NULL,
  `icon_url` varchar(500) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON>IMARY <PERSON>EY (`id`),
  KEY `idx_category_banner_category_id` (`category_id`),
  <PERSON><PERSON>Y `idx_category_banner_sort_order` (`sort_order`),
  <PERSON><PERSON>Y `idx_category_banner_landing_page_type` (`landing_page_type`),
  CONSTRAINT `fk_category_banner_category_id` FOREIGN KEY (`category_id`) REFERENCES `catalog_category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add some sample data (optional)
-- INSERT INTO `category_banner` (`category_id`, `sort_order`, `title`, `landing_page_type`, `landing_page_url`, `web_url`, `icon_url`) VALUES
-- (1, 1, 'Featured Products', 'product', '/products/featured', 'https://example.com/featured', 'https://example.com/icons/featured.png'),
-- (1, 2, 'Category Showcase', 'category', '/categories/showcase', 'https://example.com/showcase', 'https://example.com/icons/showcase.png');
